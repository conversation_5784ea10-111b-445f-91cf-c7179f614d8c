# WordPress to Supabase Migration Tool

A comprehensive Next.js application that migrates WordPress content (posts, pages, media, etc.) to Supabase with support for large-scale migrations, resume functionality, and optional static site generation.

## 🚀 Features

- **Complete WordPress Migration**: Posts, pages, media files, categories, and tags
- **Robust Error Handling**: Resume interrupted migrations from checkpoints
- **Media Management**: Automatic media file migration with URL rewriting
- **Progress Tracking**: Real-time migration progress with detailed logging
- **Static Site Generation**: Optional conversion to static files for Vercel deployment
- **Scalable Architecture**: Queue-based processing for large content volumes
- **Row Level Security**: Proper RLS policies for secure data access

## 📦 Key Libraries Used

### WordPress Integration
- **[wpapi](https://www.npmjs.com/package/wpapi)** (v1.2.2) - The most popular and well-maintained WordPress REST API client for Node.js
  - ✅ Isomorphic (works in browser and Node.js)
  - ✅ Promise-based with full REST API coverage
  - ✅ Built-in pagination and authentication support
  - ✅ 500k+ weekly downloads, actively maintained

### Database & Backend
- **[@supabase/supabase-js](https://www.npmjs.com/package/@supabase/supabase-js)** (v2.50.0) - Official Supabase client
- **[p-queue](https://www.npmjs.com/package/p-queue)** (v8.1.0) - Promise queue for controlled concurrency
- **[winston](https://www.npmjs.com/package/winston)** (v3.17.0) - Professional logging library
- **[uuid](https://www.npmjs.com/package/uuid)** (v11.1.0) - Unique identifier generation

### Frontend & UI
- **[Next.js](https://nextjs.org)** (v15.3.3) - React framework with App Router
- **[React](https://reactjs.org)** (v19.0.0) - UI library
- **[Tailwind CSS](https://tailwindcss.com)** (v4) - Utility-first CSS framework

## 🛠️ Installation & Setup

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd wp-supa-app4
npm install
```

### 2. Environment Configuration

Create a `.env.local` file with your Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ACCESS_TOKEN=your-access-token

# Default WordPress Test Site (optional)
DEFAULT_WP_SITE=https://q8hotel.co.uk
DEFAULT_WP_USERNAME=mywebmasteruk
DEFAULT_WP_PASSWORD=B8Sb 76kl UYuY 0H8V 6tJ6 Bocn
```

### 3. Database Setup

Run the database setup script to create necessary tables and functions:

```bash
node scripts/setup-database.js
```

**Alternative Manual Setup:**
If the script fails, manually run the SQL in `scripts/setup-supabase.sql` in your Supabase SQL Editor.

### 4. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to access the application.

## 🎯 Usage Guide

### Step 1: WordPress Connection
1. Enter your WordPress site URL
2. Provide admin username and app password
3. Click "Connect to WordPress" to test the connection

### Step 2: Content Discovery
- The app automatically discovers all available content
- View counts for posts, pages, media, categories, and tags
- Review the content summary before proceeding

### Step 3: Migration
1. Click "Start Migration" to begin the process
2. Monitor real-time progress with detailed logging
3. Migration creates unique table suffixes to avoid conflicts

### Step 4: Static Site Generation (Optional)
- Generate static HTML files from migrated content
- Deploy-ready files for Vercel or other static hosts
- Customizable templates and styling

## 🏗️ Architecture Overview

### Migration Process
1. **Connection Testing**: Validates WordPress credentials
2. **Content Discovery**: Scans and counts all content types
3. **Database Setup**: Creates migration tables with unique suffixes
4. **Media Migration**: Downloads and uploads media files to Supabase Storage
5. **Content Migration**: Migrates posts/pages with updated media URLs
6. **Progress Tracking**: Logs progress with checkpoint system
7. **Summary Generation**: Provides detailed migration report

### Error Handling & Resume
- **Checkpoint System**: Saves progress at regular intervals
- **Queue Management**: Handles failed items with retry logic
- **Resume Capability**: Continues from last successful checkpoint
- **Detailed Logging**: Winston-based logging for debugging

## 📊 Database Schema

The app creates the following tables with unique suffixes:

- `wp_posts_{suffix}` - WordPress posts
- `wp_pages_{suffix}` - WordPress pages
- `wp_media_{suffix}` - Media files metadata
- `migration_jobs` - Migration job tracking

## 🔒 Security Features

- **Row Level Security (RLS)**: Enabled on all migration tables
- **Service Role Access**: Write operations restricted to service role
- **Authenticated Read**: Read access for authenticated users only
- **Secure Media Storage**: Public bucket with size and type restrictions

## 🚀 Deployment

### Vercel Deployment
1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy with automatic builds

### Manual Deployment
```bash
npm run build
npm start
```

## 🧪 Testing

Test with the provided default WordPress site:
- **Site**: https://q8hotel.co.uk
- **Username**: mywebmasteruk
- **App Password**: B8Sb 76kl UYuY 0H8V 6tJ6 Bocn

## 📝 API Endpoints

- `POST /api/wordpress/connect` - Test WordPress connection
- `POST /api/wordpress/discover` - Discover WordPress content
- `POST /api/migrate/start` - Start migration process
- `GET /api/migrate/status/{jobId}` - Get migration status
- `GET /api/supabase/summary/{suffix}` - Get migration summary
- `POST /api/static/generate` - Generate static site files

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.
