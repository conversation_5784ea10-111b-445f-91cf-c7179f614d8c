import { NextResponse } from 'next/server';
import { WordPressMigrator } from '../../../../lib/migration.js';

export async function POST(request) {
  try {
    const { wpConfig, discoveredContent } = await request.json();

    if (!wpConfig || !wpConfig.wpSite || !wpConfig.wpUsername || !wpConfig.wpPassword) {
      return NextResponse.json({
        success: false,
        message: 'Missing WordPress configuration'
      }, { status: 400 });
    }

    // Create migrator instance
    const migrator = new WordPressMigrator({
      siteUrl: wpConfig.wpSite,
      username: wpConfig.wpUsername,
      password: wpConfig.wpPassword
    });

    // Initialize migration
    const initResult = await migrator.initialize();

    if (!initResult.success) {
      return NextResponse.json({
        success: false,
        message: 'Failed to initialize migration'
      }, { status: 500 });
    }

    // Start migration in background
    migrator.startMigration().catch(error => {
      console.error('Migration failed:', error);
    });

    return NextResponse.json({
      success: true,
      jobId: initResult.jobId,
      suffix: initResult.suffix,
      message: 'Migration started successfully'
    });

  } catch (error) {
    console.error('Migration start error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
