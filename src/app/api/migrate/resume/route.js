import { NextResponse } from 'next/server';
import { WordPressMigrator } from '../../../../lib/migration.js';

export async function POST(request) {
  try {
    const { jobId } = await request.json();

    if (!jobId) {
      return NextResponse.json({
        success: false,
        message: 'Missing jobId parameter'
      }, { status: 400 });
    }

    // Resume migration using the static method
    const result = await WordPressMigrator.resumeMigration(jobId);

    return NextResponse.json({
      success: result.success,
      message: result.message || 'Migration resumed successfully',
      jobId: jobId
    });

  } catch (error) {
    console.error('Migration resume error:', error);
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to resume migration'
    }, { status: 500 });
  }
}
