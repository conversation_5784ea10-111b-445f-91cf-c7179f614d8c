import { NextResponse } from 'next/server';
import { getMigrationJob } from '../../../../lib/supabase.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        success: false,
        message: 'Missing jobId parameter'
      }, { status: 400 });
    }

    const job = await getMigrationJob(jobId);

    if (!job) {
      return NextResponse.json({
        success: false,
        message: 'Migration job not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      progress: {
        status: job.status,
        posts: {
          total: job.progress?.posts?.total || 0,
          completed: job.progress?.posts?.completed || 0
        },
        pages: {
          total: job.progress?.pages?.total || 0,
          completed: job.progress?.pages?.completed || 0
        },
        media: {
          total: job.progress?.media?.total || 0,
          completed: job.progress?.media?.completed || 0
        },
        suffix: job.suffix
      }
    });

  } catch (error) {
    console.error('Migration status error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
