'use client';

import { useState, useEffect } from 'react';

export default function Settings() {
  const [settings, setSettings] = useState({
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    supabaseServiceKey: '',
    supabaseAccessToken: ''
  });

  const [isSaving, setIsSaving] = useState(false);
  const [saveResult, setSaveResult] = useState(null);

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('supabaseSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const saveSettings = async () => {
    setIsSaving(true);
    setSaveResult(null);

    try {
      // Save to localStorage
      localStorage.setItem('supabaseSettings', JSON.stringify(settings));
      
      // Test connection
      const testResult = await testSupabaseConnection();
      
      setSaveResult({
        success: true,
        message: 'Settings saved successfully!',
        connectionTest: testResult
      });
    } catch (error) {
      setSaveResult({
        success: false,
        message: 'Failed to save settings'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const testSupabaseConnection = async () => {
    try {
      const response = await fetch('/api/supabase/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Connection test failed'
      };
    }
  };

  const resetToDefaults = () => {
    setSettings({
      supabaseUrl: 'https://fbsxghuwkkrzjngcheau.supabase.co',
      supabaseServiceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZic3hnaHV3a2tyempuZ2NoZWF1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODc3MDMxOSwiZXhwIjoyMDY0MzQ2MzE5fQ.LwVTCgr0l1XKrs_zwOzgt-ynnG_F_OxU2dGZgp-yy3g',
      supabaseAccessToken: 'sbp_3f157a28795ca4c5be41a72b386e2383057f202'
    });
    setSaveResult(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            Supabase Settings
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Configure your Supabase connection settings
          </p>
        </header>

        <div className="max-w-2xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                Configuration
              </h2>
              <a
                href="/"
                className="px-4 py-2 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors"
              >
                ← Back to Migration
              </a>
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="supabaseUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Supabase Project URL
                </label>
                <input
                  type="url"
                  id="supabaseUrl"
                  name="supabaseUrl"
                  value={settings.supabaseUrl}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="https://your-project.supabase.co"
                  required
                />
              </div>

              <div>
                <label htmlFor="supabaseServiceKey" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Supabase Service Role Key
                </label>
                <textarea
                  id="supabaseServiceKey"
                  name="supabaseServiceKey"
                  value={settings.supabaseServiceKey}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono text-sm"
                  placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  required
                />
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Found in your Supabase project settings under API → service_role key
                </p>
              </div>

              <div>
                <label htmlFor="supabaseAccessToken" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Supabase Personal Access Token
                </label>
                <input
                  type="password"
                  id="supabaseAccessToken"
                  name="supabaseAccessToken"
                  value={settings.supabaseAccessToken}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono"
                  placeholder="sbp_..."
                  required
                />
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Generate in your Supabase account settings → Access Tokens
                </p>
              </div>

              <div className="flex gap-4">
                <button
                  onClick={saveSettings}
                  disabled={isSaving}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
                >
                  {isSaving ? 'Saving...' : 'Save Settings'}
                </button>

                <button
                  onClick={resetToDefaults}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-md transition-colors"
                >
                  Reset to Defaults
                </button>
              </div>

              {saveResult && (
                <div className={`p-4 rounded-md ${
                  saveResult.success 
                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' 
                    : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                }`}>
                  <p className={`text-sm font-medium ${
                    saveResult.success 
                      ? 'text-green-800 dark:text-green-200' 
                      : 'text-red-800 dark:text-red-200'
                  }`}>
                    {saveResult.message}
                  </p>
                  
                  {saveResult.connectionTest && (
                    <div className="mt-2">
                      <p className={`text-sm ${
                        saveResult.connectionTest.success
                          ? 'text-green-700 dark:text-green-300'
                          : 'text-red-700 dark:text-red-300'
                      }`}>
                        Connection Test: {saveResult.connectionTest.success ? 'Success' : 'Failed'}
                      </p>
                      {saveResult.connectionTest.message && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {saveResult.connectionTest.message}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Information Section */}
            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-3">
                How to Get Your Supabase Credentials
              </h3>
              <ol className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                <li className="flex items-start">
                  <span className="font-semibold mr-2">1.</span>
                  Go to your Supabase project dashboard
                </li>
                <li className="flex items-start">
                  <span className="font-semibold mr-2">2.</span>
                  Navigate to Settings → API
                </li>
                <li className="flex items-start">
                  <span className="font-semibold mr-2">3.</span>
                  Copy the Project URL and service_role key
                </li>
                <li className="flex items-start">
                  <span className="font-semibold mr-2">4.</span>
                  For the Access Token, go to your account settings → Access Tokens
                </li>
                <li className="flex items-start">
                  <span className="font-semibold mr-2">5.</span>
                  Generate a new token with appropriate permissions
                </li>
              </ol>
            </div>

            {/* Security Warning */}
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                ⚠️ Security Notice
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                These credentials are stored locally in your browser. Never share your service role key or access token. 
                Consider using environment variables for production deployments.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
