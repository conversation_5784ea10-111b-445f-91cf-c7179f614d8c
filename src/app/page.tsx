'use client';

import { useState } from 'react';
import MigrationForm from '../components/MigrationForm';
import ProgressTracker from '../components/ProgressTracker';
import ContentSummary from '../components/ContentSummary';

export default function Home() {
  const [currentStep, setCurrentStep] = useState('form'); // form, progress, summary
  const [migrationData, setMigrationData] = useState(null);

  const handleMigrationStart = (data) => {
    setMigrationData(data);
    setCurrentStep('progress');
  };

  const handleMigrationComplete = (summary) => {
    setMigrationData({ ...migrationData, summary });
    setCurrentStep('summary');
  };

  const resetMigration = () => {
    setCurrentStep('form');
    setMigrationData(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            WordPress to Supabase Migrator
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Migrate your WordPress content to Supabase with ease
          </p>
        </header>

        <div className="max-w-4xl mx-auto">
          {currentStep === 'form' && (
            <MigrationForm onMigrationStart={handleMigrationStart} />
          )}

          {currentStep === 'progress' && (
            <ProgressTracker
              migrationData={migrationData}
              onComplete={handleMigrationComplete}
              onReset={resetMigration}
            />
          )}

          {currentStep === 'summary' && (
            <ContentSummary
              migrationData={migrationData}
              onReset={resetMigration}
            />
          )}
        </div>

        <footer className="text-center mt-16 text-gray-500 dark:text-gray-400">
          <p>Built with Next.js, Supabase, and the WordPress REST API</p>
        </footer>
      </div>
    </div>
  );
}
