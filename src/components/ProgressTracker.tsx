'use client';

import { useEffect, useState } from 'react';
import {
  Box,
  Progress,
  Text,
  VStack,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Spinner,
  Container
} from '@chakra-ui/react';

interface ProgressTrackerProps {
  migrationData: any;
  onComplete: (summary: any) => void;
  onReset: () => void;
}

export default function ProgressTracker({ migrationData, onComplete, onReset }: ProgressTrackerProps) {
  const [progress, setProgress] = useState({
    status: 'initializing',
    posts: { total: 0, completed: 0 },
    pages: { total: 0, completed: 0 },
    media: { total: 0, completed: 0 },
    overall: 0
  });
  const [jobId, setJobId] = useState(null);
  const [logs, setLogs] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    startMigration();
  }, []);

  const startMigration = async () => {
    try {
      addLog('Starting migration process...');
      
      const response = await fetch('/api/migrate/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(migrationData),
      });

      const result = await response.json();
      
      if (result.success) {
        setJobId(result.jobId);
        addLog(`Migration job created: ${result.jobId}`);
        addLog(`Using table suffix: ${result.suffix}`);
        
        // Start polling for progress
        pollProgress(result.jobId);
      } else {
        setError(result.message || 'Failed to start migration');
        addLog(`Error: ${result.message}`);
      }
    } catch (error) {
      setError('Failed to start migration');
      addLog(`Error: ${error.message}`);
    }
  };

  const pollProgress = async (jobId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/migrate/status?jobId=${jobId}`);
        const result = await response.json();

        if (result.success) {
          setProgress(result.progress);
          
          // Add progress logs
          if (result.progress.status !== progress.status) {
            addLog(`Status: ${result.progress.status}`);
          }

          // Check if migration is complete
          if (result.progress.status === 'completed') {
            clearInterval(pollInterval);
            addLog('Migration completed successfully!');
            
            // Get final summary
            const summaryResponse = await fetch(`/api/supabase/summary?suffix=${result.progress.suffix}`);
            const summary = await summaryResponse.json();
            
            onComplete(summary);
          } else if (result.progress.status === 'failed') {
            clearInterval(pollInterval);
            setError('Migration failed');
            addLog('Migration failed. Check logs for details.');
          }
        }
      } catch (error) {
        console.error('Error polling progress:', error);
        addLog(`Polling error: ${error.message}`);
      }
    }, 2000); // Poll every 2 seconds

    // Cleanup interval after 30 minutes to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30 * 60 * 1000);
  };

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const calculateOverallProgress = () => {
    const totalItems = progress.posts.total + progress.pages.total + progress.media.total;
    const completedItems = progress.posts.completed + progress.pages.completed + progress.media.completed;
    return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
  };

  const getProgressBarColor = (percentage) => {
    if (percentage < 30) return 'bg-red-500';
    if (percentage < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const overallProgress = calculateOverallProgress();

  return (
    <Container maxW="container.md" p={6}>
      <VStack spacing={6} align="stretch">
        <HStack justifyContent="space-between">
          <Text fontSize="xl" fontWeight="bold">Migration Progress</Text>
          <Badge colorScheme={getStatusColor(progress.status)}>
            {progress.status.toUpperCase()}
          </Badge>
        </HStack>

        <Box>
          <Text mb={2}>Posts</Text>
          <Progress 
            value={getProgressPercentage(progress.posts.completed, progress.posts.total)}
            size="sm"
            colorScheme="blue"
          />
          <Text fontSize="sm" mt={1}>
            {progress.posts.completed} / {progress.posts.total}
          </Text>
        </Box>

        <Box>
          <Text mb={2}>Pages</Text>
          <Progress 
            value={getProgressPercentage(progress.pages.completed, progress.pages.total)}
            size="sm"
            colorScheme="green"
          />
          <Text fontSize="sm" mt={1}>
            {progress.pages.completed} / {progress.pages.total}
          </Text>
        </Box>

        <Box>
          <Text mb={2}>Media</Text>
          <Progress 
            value={getProgressPercentage(progress.media.completed, progress.media.total)}
            size="sm"
            colorScheme="purple"
          />
          <Text fontSize="sm" mt={1}>
            {progress.media.completed} / {progress.media.total}
          </Text>
        </Box>

        {progress.status === 'completed' && (
          <Box p={4} bg="green.50" color="green.600" borderRadius="md">
            <Text>Migration completed successfully!</Text>
            <Text fontSize="sm" mt={2}>
              Tables and buckets are using suffix: {progress.suffix}
            </Text>
          </Box>
        )}
      </VStack>
    </Container>
  );
}
