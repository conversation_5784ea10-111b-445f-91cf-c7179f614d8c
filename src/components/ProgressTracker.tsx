'use client';

import { useEffect, useState } from 'react';

interface ProgressTrackerProps {
  migrationData: any;
  onComplete: (summary: any) => void;
  onReset: () => void;
}

export default function ProgressTracker({ migrationData, onComplete, onReset }: ProgressTrackerProps) {
  const [progress, setProgress] = useState({
    status: 'initializing',
    posts: { total: 0, completed: 0 },
    pages: { total: 0, completed: 0 },
    media: { total: 0, completed: 0 },
    overall: 0
  });
  const [jobId, setJobId] = useState(null);
  const [logs, setLogs] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    startMigration();
  }, []);

  const startMigration = async () => {
    try {
      addLog('Starting migration process...');
      
      const response = await fetch('/api/migrate/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(migrationData),
      });

      const result = await response.json();
      
      if (result.success) {
        setJobId(result.jobId);
        addLog(`Migration job created: ${result.jobId}`);
        addLog(`Using table suffix: ${result.suffix}`);
        
        // Start polling for progress
        pollProgress(result.jobId);
      } else {
        setError(result.message || 'Failed to start migration');
        addLog(`Error: ${result.message}`);
      }
    } catch (error) {
      setError('Failed to start migration');
      addLog(`Error: ${error.message}`);
    }
  };

  const pollProgress = async (jobId) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/migrate/status?jobId=${jobId}`);
        const result = await response.json();

        if (result.success) {
          setProgress(result.progress);
          
          // Add progress logs
          if (result.progress.status !== progress.status) {
            addLog(`Status: ${result.progress.status}`);
          }

          // Check if migration is complete
          if (result.progress.status === 'completed') {
            clearInterval(pollInterval);
            addLog('Migration completed successfully!');
            
            // Get final summary
            const summaryResponse = await fetch(`/api/supabase/summary?suffix=${result.progress.suffix}`);
            const summary = await summaryResponse.json();
            
            onComplete(summary);
          } else if (result.progress.status === 'failed') {
            clearInterval(pollInterval);
            setError('Migration failed');
            addLog('Migration failed. Check logs for details.');
          }
        }
      } catch (error) {
        console.error('Error polling progress:', error);
        addLog(`Polling error: ${error.message}`);
      }
    }, 2000); // Poll every 2 seconds

    // Cleanup interval after 30 minutes to prevent infinite polling
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30 * 60 * 1000);
  };

  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const calculateOverallProgress = () => {
    const totalItems = progress.posts.total + progress.pages.total + progress.media.total;
    const completedItems = progress.posts.completed + progress.pages.completed + progress.media.completed;
    return totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0;
  };

  const getProgressPercentage = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const overallProgress = calculateOverallProgress();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Migration Progress
          </h2>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(progress.status)}`}>
            {progress.status.toUpperCase()}
          </span>
        </div>

        {/* Overall Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Overall Progress
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {overallProgress}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${overallProgress}%` }}
            ></div>
          </div>
        </div>

        {/* Posts Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Posts
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {progress.posts.completed} / {progress.posts.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage(progress.posts.completed, progress.posts.total)}%` }}
            ></div>
          </div>
        </div>

        {/* Pages Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Pages
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {progress.pages.completed} / {progress.pages.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage(progress.pages.completed, progress.pages.total)}%` }}
            ></div>
          </div>
        </div>

        {/* Media Progress */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Media Files
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {progress.media.completed} / {progress.media.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              className="bg-purple-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage(progress.media.completed, progress.media.total)}%` }}
            ></div>
          </div>
        </div>

        {/* Status Messages */}
        {progress.status === 'completed' && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800 dark:text-green-200">
                  Migration completed successfully!
                </p>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Tables and buckets are using suffix: {progress.suffix}
                </p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800 dark:text-red-200">
                  {error}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Activity Logs */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
            Activity Log
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No activity yet...
              </p>
            ) : (
              <div className="space-y-1">
                {logs.map((log, index) => (
                  <div key={index} className="text-sm font-mono text-gray-600 dark:text-gray-300">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button
            type="button"
            onClick={onReset}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
          >
            Start New Migration
          </button>

          {progress.status === 'completed' && (
            <button
              type="button"
              onClick={() => {
                // This will be handled by the parent component
                onComplete({ suffix: progress.suffix });
              }}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
            >
              View Summary
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
