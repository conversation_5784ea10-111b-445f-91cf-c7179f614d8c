# Getting Started with Word<PERSON>ress to Supabase Migration

This guide will walk you through setting up and using the WordPress to Supabase migration tool.

## 🚀 Quick Start

### 1. Prerequisites

- Node.js 18+ installed
- A Supabase project (free tier works fine)
- WordPress site with REST API enabled
- WordPress App Password for authentication

### 2. <PERSON><PERSON> and Install

```bash
git clone <your-repo-url>
cd wp-supa-app4
npm install
```

### 3. Get Your Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** → **API**
4. Copy the following values:
   - **Project URL** (e.g., `https://your-project.supabase.co`)
   - **anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **service_role secret key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 4. Configure Environment Variables

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_ACCESS_TOKEN=your-access-token-here

# Optional: Default WordPress Test Site
DEFAULT_WP_SITE=https://your-wordpress-site.com
DEFAULT_WP_USERNAME=your-wp-username
DEFAULT_WP_PASSWORD=your-wp-app-password
```

### 5. Set Up Database

Run the database setup script:

```bash
npm run setup
```

This will create the necessary tables and functions in your Supabase database.

**If the script fails**, manually run the SQL in `scripts/setup-supabase.sql` in your Supabase SQL Editor.

### 6. Start the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📋 WordPress Setup

### Creating an App Password

1. Log into your WordPress admin dashboard
2. Go to **Users** → **Profile**
3. Scroll down to **Application Passwords**
4. Enter a name (e.g., "Migration Tool")
5. Click **Add New Application Password**
6. Copy the generated password (format: `xxxx xxxx xxxx xxxx xxxx xxxx`)

### WordPress Requirements

- WordPress 4.7+ (REST API enabled by default)
- Admin user account
- App Password authentication
- Publicly accessible WordPress site

## 🔧 Using the Migration Tool

### Step 1: Test Connection

1. Enter your WordPress site URL
2. Enter your admin username
3. Enter your App Password
4. Click **"Connect to WordPress"**

### Step 2: Discover Content

The tool will automatically scan your WordPress site and show:
- Number of posts
- Number of pages
- Number of media files
- Number of categories and tags

### Step 3: Start Migration

1. Review the discovered content
2. Click **"Start Migration"**
3. Monitor the real-time progress
4. Wait for completion

### Step 4: Review Results

After migration, you'll see:
- Summary of migrated content
- Database table names with unique suffixes
- Links to view data in Supabase
- Option to generate static site files

## 📊 Understanding the Migration

### What Gets Migrated

- **Posts**: Title, content, excerpt, slug, status, categories, tags
- **Pages**: Title, content, slug, status, parent relationships
- **Media**: Files uploaded to Supabase Storage with metadata
- **URLs**: Media URLs in content are automatically rewritten

### Database Structure

The tool creates tables with unique 4-character suffixes:
- `wp_posts_xxxx` - WordPress posts
- `wp_pages_xxxx` - WordPress pages
- `wp_media_xxxx` - Media file metadata
- `migration_jobs` - Migration tracking

### Storage Structure

Media files are stored in Supabase Storage buckets:
- Bucket name: `wp-media-xxxx`
- Files retain original names with ID prefixes
- Public access enabled for web display

## 🔒 Security Features

### Row Level Security (RLS)

All tables have RLS enabled with policies:
- **Service Role**: Full access (for migration)
- **Authenticated Users**: Read-only access
- **Anonymous Users**: No access

### Data Protection

- Service role key used only for server-side operations
- Client-side operations use anon key
- Media files stored in public buckets (as intended for web display)

## 🚨 Troubleshooting

### Common Issues

**"Connection failed"**
- Check WordPress URL (include https://)
- Verify App Password is correct
- Ensure WordPress REST API is accessible

**"Database setup failed"**
- Check Supabase credentials
- Ensure service role key has proper permissions
- Try manual SQL execution in Supabase dashboard

**"Migration hangs"**
- Check browser console for errors
- Large sites may take time - be patient
- Use resume functionality if needed

**"Media files not uploading"**
- Check Supabase Storage quotas
- Verify file types are allowed
- Check network connectivity

### Getting Help

1. Check the browser console for error messages
2. Review the migration logs in the UI
3. Check Supabase logs in the dashboard
4. Verify all environment variables are set correctly

## 🎯 Next Steps

After successful migration:

1. **Review Data**: Check your Supabase dashboard
2. **Test Queries**: Try fetching data via Supabase client
3. **Set Up Your App**: Configure your application to use the migrated data
4. **Static Site**: Generate static files if needed
5. **Deploy**: Deploy your new application

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [WordPress REST API Handbook](https://developer.wordpress.org/rest-api/)
- [Next.js Documentation](https://nextjs.org/docs)

## 🤝 Support

If you encounter issues:
1. Check this guide first
2. Review the main README.md
3. Check existing GitHub issues
4. Create a new issue with detailed information

Happy migrating! 🚀
