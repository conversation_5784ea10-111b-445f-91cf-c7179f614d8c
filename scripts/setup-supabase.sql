-- WordPress to Supabase Migration Setup Script
-- Run this in your Supabase SQL Editor

-- Create migration_jobs table first (no suffix needed)
CREATE TABLE IF NOT EXISTS migration_jobs (
  id SERIAL PRIMARY KEY,
  domain TEXT NOT NULL,
  suffix TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  progress JSONB DEFAULT '{}',
  total_items INTEGER DEFAULT 0,
  completed_items INTEGER DEFAULT 0,
  error_log TEXT[],
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  last_checkpoint JSONB DEFAULT '{}'
);

-- Enable RLS on migration_jobs
ALTER TABLE migration_jobs ENABLE ROW LEVEL SECURITY;

-- Create policy for migration_jobs (allow all operations for service role)
CREATE POLICY "migration_jobs_service_policy" ON migration_jobs
  FOR ALL TO service_role
  USING (true)
  WITH CHECK (true);

-- Create policy for migration_jobs (read-only for authenticated users)
CREATE POLICY "migration_jobs_read_policy" ON migration_jobs
  FOR SELECT TO authenticated
  USING (true);

-- Function to create migration tables with suffix
CREATE OR REPLACE FUNCTION create_migration_tables(table_suffix TEXT)
RETURNS JSONB AS $$
DECLARE
  result JSONB := '{}';
BEGIN
  -- Create wp_posts table
  EXECUTE format('
    CREATE TABLE IF NOT EXISTS wp_posts_%s (
      id SERIAL PRIMARY KEY,
      wp_id INTEGER NOT NULL,
      title TEXT,
      content TEXT,
      excerpt TEXT,
      slug TEXT,
      status TEXT,
      type TEXT,
      author_id INTEGER,
      featured_media_id INTEGER,
      categories TEXT[],
      tags TEXT[],
      created_at TIMESTAMP WITH TIME ZONE,
      modified_at TIMESTAMP WITH TIME ZONE,
      migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )', table_suffix);

  -- Create wp_pages table
  EXECUTE format('
    CREATE TABLE IF NOT EXISTS wp_pages_%s (
      id SERIAL PRIMARY KEY,
      wp_id INTEGER NOT NULL,
      title TEXT,
      content TEXT,
      slug TEXT,
      status TEXT,
      parent_id INTEGER,
      template TEXT,
      featured_media_id INTEGER,
      created_at TIMESTAMP WITH TIME ZONE,
      modified_at TIMESTAMP WITH TIME ZONE,
      migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )', table_suffix);

  -- Create wp_media table
  EXECUTE format('
    CREATE TABLE IF NOT EXISTS wp_media_%s (
      id SERIAL PRIMARY KEY,
      wp_id INTEGER NOT NULL,
      title TEXT,
      filename TEXT,
      original_url TEXT,
      supabase_url TEXT,
      mime_type TEXT,
      file_size INTEGER,
      width INTEGER,
      height INTEGER,
      alt_text TEXT,
      caption TEXT,
      created_at TIMESTAMP WITH TIME ZONE,
      migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )', table_suffix);

  -- Enable RLS on all tables
  EXECUTE format('ALTER TABLE wp_posts_%s ENABLE ROW LEVEL SECURITY', table_suffix);
  EXECUTE format('ALTER TABLE wp_pages_%s ENABLE ROW LEVEL SECURITY', table_suffix);
  EXECUTE format('ALTER TABLE wp_media_%s ENABLE ROW LEVEL SECURITY', table_suffix);

  -- Create policies for wp_posts
  EXECUTE format('
    CREATE POLICY "wp_posts_%s_service_policy" ON wp_posts_%s
      FOR ALL TO service_role
      USING (true)
      WITH CHECK (true)', table_suffix, table_suffix);

  EXECUTE format('
    CREATE POLICY "wp_posts_%s_read_policy" ON wp_posts_%s
      FOR SELECT TO authenticated
      USING (true)', table_suffix, table_suffix);

  -- Create policies for wp_pages
  EXECUTE format('
    CREATE POLICY "wp_pages_%s_service_policy" ON wp_pages_%s
      FOR ALL TO service_role
      USING (true)
      WITH CHECK (true)', table_suffix, table_suffix);

  EXECUTE format('
    CREATE POLICY "wp_pages_%s_read_policy" ON wp_pages_%s
      FOR SELECT TO authenticated
      USING (true)', table_suffix, table_suffix);

  -- Create policies for wp_media
  EXECUTE format('
    CREATE POLICY "wp_media_%s_service_policy" ON wp_media_%s
      FOR ALL TO service_role
      USING (true)
      WITH CHECK (true)', table_suffix, table_suffix);

  EXECUTE format('
    CREATE POLICY "wp_media_%s_read_policy" ON wp_media_%s
      FOR SELECT TO authenticated
      USING (true)', table_suffix, table_suffix);

  result := jsonb_build_object(
    'success', true,
    'tables_created', ARRAY[
      'wp_posts_' || table_suffix,
      'wp_pages_' || table_suffix,
      'wp_media_' || table_suffix
    ]
  );

  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION create_migration_tables(TEXT) TO service_role;

-- Function to get migration summary
CREATE OR REPLACE FUNCTION get_migration_summary(table_suffix TEXT)
RETURNS JSONB AS $$
DECLARE
  posts_count INTEGER := 0;
  pages_count INTEGER := 0;
  media_count INTEGER := 0;
  result JSONB;
BEGIN
  -- Get posts count
  EXECUTE format('SELECT COUNT(*) FROM wp_posts_%s', table_suffix) INTO posts_count;
  
  -- Get pages count
  EXECUTE format('SELECT COUNT(*) FROM wp_pages_%s', table_suffix) INTO pages_count;
  
  -- Get media count
  EXECUTE format('SELECT COUNT(*) FROM wp_media_%s', table_suffix) INTO media_count;

  result := jsonb_build_object(
    'posts', posts_count,
    'pages', pages_count,
    'media', media_count,
    'total', posts_count + pages_count + media_count
  );

  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'error', SQLERRM,
      'posts', 0,
      'pages', 0,
      'media', 0,
      'total', 0
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION get_migration_summary(TEXT) TO service_role;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_migration_jobs_domain ON migration_jobs(domain);
CREATE INDEX IF NOT EXISTS idx_migration_jobs_status ON migration_jobs(status);
CREATE INDEX IF NOT EXISTS idx_migration_jobs_suffix ON migration_jobs(suffix);

-- Success message
SELECT 'Supabase setup completed successfully!' as message;
