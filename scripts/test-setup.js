#!/usr/bin/env node

/**
 * Test Setup Script for WordPress to Supabase Migration
 * 
 * This script tests the basic functionality of the migration tool
 * to ensure everything is set up correctly.
 */

import { createClient } from '@supabase/supabase-js';
import { testWordPressConnection } from '../lib/wordpress.js';
import { validateSupabaseConfig } from '../lib/utils.js';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, colors.green);
}

function error(message) {
  log(`❌ ${message}`, colors.red);
}

function warning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function info(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

async function testSupabaseConnection() {
  info('Testing Supabase connection...');
  
  try {
    // Validate configuration
    const configValidation = validateSupabaseConfig();
    if (!configValidation.valid) {
      error(`Supabase configuration invalid: ${configValidation.error}`);
      return false;
    }
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test basic connection
    const { data, error: connectionError } = await supabase
      .from('migration_jobs')
      .select('count', { count: 'exact', head: true });
    
    if (connectionError) {
      if (connectionError.message.includes('does not exist')) {
        warning('migration_jobs table does not exist. Run: npm run setup');
        return false;
      } else {
        error(`Supabase connection failed: ${connectionError.message}`);
        return false;
      }
    }
    
    success('Supabase connection successful');
    
    // Test function availability
    try {
      const { data: funcData, error: funcError } = await supabase.rpc('create_migration_tables', {
        table_suffix: 'test'
      });
      
      if (funcError) {
        warning('Migration functions not available. Run: npm run setup');
        return false;
      }
      
      success('Migration functions available');
      
      // Clean up test tables
      const testTables = ['wp_posts_test', 'wp_pages_test', 'wp_media_test'];
      for (const table of testTables) {
        try {
          await supabase.rpc('exec', { sql: `DROP TABLE IF EXISTS ${table}` });
        } catch (err) {
          // Ignore cleanup errors
        }
      }
      
    } catch (err) {
      warning('Could not test migration functions. Database setup may be incomplete.');
    }
    
    return true;
    
  } catch (err) {
    error(`Supabase test failed: ${err.message}`);
    return false;
  }
}

async function testWordPressConnectionSetup() {
  info('Testing WordPress connection...');
  
  const wpSite = process.env.DEFAULT_WP_SITE || 'https://q8hotel.co.uk';
  const wpUsername = process.env.DEFAULT_WP_USERNAME || 'mywebmasteruk';
  const wpPassword = process.env.DEFAULT_WP_PASSWORD || 'B8Sb 76kl UYuY 0H8V 6tJ6 Bocn';
  
  if (!wpSite || !wpUsername || !wpPassword) {
    warning('WordPress credentials not configured. Skipping WordPress test.');
    return true;
  }
  
  try {
    const result = await testWordPressConnection(wpSite, wpUsername, wpPassword);
    
    if (result.success) {
      success(`WordPress connection successful: ${wpSite}`);
      info(`Posts found: ${result.data?.postsFound ? 'Yes' : 'No'}`);
      return true;
    } else {
      error(`WordPress connection failed: ${result.message}`);
      return false;
    }
    
  } catch (err) {
    error(`WordPress test failed: ${err.message}`);
    return false;
  }
}

async function testEnvironmentVariables() {
  info('Checking environment variables...');
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  const optionalVars = [
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_ACCESS_TOKEN',
    'DEFAULT_WP_SITE',
    'DEFAULT_WP_USERNAME',
    'DEFAULT_WP_PASSWORD'
  ];
  
  let allGood = true;
  
  // Check required variables
  for (const varName of requiredVars) {
    if (process.env[varName]) {
      success(`${varName} is set`);
    } else {
      error(`${varName} is missing`);
      allGood = false;
    }
  }
  
  // Check optional variables
  for (const varName of optionalVars) {
    if (process.env[varName]) {
      success(`${varName} is set`);
    } else {
      warning(`${varName} is not set (optional)`);
    }
  }
  
  return allGood;
}

async function testDependencies() {
  info('Checking dependencies...');
  
  try {
    // Test critical imports
    await import('../lib/wordpress.js');
    await import('../lib/supabase.js');
    await import('../lib/migration.js');
    await import('../lib/utils.js');
    
    success('All core modules can be imported');
    return true;
    
  } catch (err) {
    error(`Dependency test failed: ${err.message}`);
    return false;
  }
}

async function main() {
  log(`${colors.bold}🧪 WordPress to Supabase Migration - Setup Test${colors.reset}\n`);
  
  const tests = [
    { name: 'Environment Variables', fn: testEnvironmentVariables },
    { name: 'Dependencies', fn: testDependencies },
    { name: 'Supabase Connection', fn: testSupabaseConnection },
    { name: 'WordPress Connection', fn: testWordPressConnectionSetup }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    log(`\n${colors.bold}Testing ${test.name}...${colors.reset}`);
    
    try {
      const result = await test.fn();
      if (result) {
        passedTests++;
      }
    } catch (err) {
      error(`Test "${test.name}" threw an error: ${err.message}`);
    }
  }
  
  log(`\n${colors.bold}Test Results:${colors.reset}`);
  log(`Passed: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    success('\n🎉 All tests passed! Your setup is ready for migration.');
    log('\nNext steps:');
    log('1. Run: npm run dev');
    log('2. Open: http://localhost:3000');
    log('3. Test the migration with your WordPress site');
  } else {
    error('\n❌ Some tests failed. Please fix the issues above before proceeding.');
    log('\nCommon fixes:');
    log('1. Check your .env.local file');
    log('2. Run: npm run setup');
    log('3. Verify your Supabase project settings');
    log('4. Check your WordPress site accessibility');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

main().catch(err => {
  error(`Test script failed: ${err.message}`);
  process.exit(1);
});
