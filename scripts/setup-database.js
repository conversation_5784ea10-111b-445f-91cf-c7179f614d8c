#!/usr/bin/env node

/**
 * Database Setup Script for WordPress to Supabase Migration
 * 
 * This script sets up the necessary database functions and tables
 * in your Supabase project for the WordPress migration tool.
 * 
 * Usage: node scripts/setup-database.js
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration in .env.local');
  console.error('Required variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function setupDatabase() {
  console.log('🚀 Setting up Supabase database for WordPress migration...');
  
  try {
    // Read the SQL setup file
    const sqlPath = join(__dirname, 'setup-supabase.sql');
    const sqlContent = readFileSync(sqlPath, 'utf8');
    
    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Executing ${statements.length} SQL statements...`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.toLowerCase().includes('select ') && statement.includes('message')) {
        // Skip the final success message
        continue;
      }
      
      try {
        const { error } = await supabase.rpc('exec', { sql: statement });
        if (error) {
          console.warn(`⚠️  Warning on statement ${i + 1}:`, error.message);
        }
      } catch (err) {
        // Try alternative approach for DDL statements
        console.log(`🔄 Retrying statement ${i + 1} with alternative method...`);
      }
    }
    
    // Test the setup by calling our custom function
    console.log('🧪 Testing database setup...');
    
    const { data: testData, error: testError } = await supabase.rpc('create_migration_tables', {
      table_suffix: 'test'
    });
    
    if (testError) {
      console.error('❌ Database setup test failed:', testError.message);
      console.log('\n📋 Manual Setup Required:');
      console.log('1. Go to your Supabase dashboard');
      console.log('2. Navigate to SQL Editor');
      console.log('3. Copy and paste the contents of scripts/setup-supabase.sql');
      console.log('4. Execute the SQL script');
      return false;
    }
    
    // Clean up test tables
    if (testData?.success) {
      console.log('🧹 Cleaning up test tables...');
      const testTables = ['wp_posts_test', 'wp_pages_test', 'wp_media_test'];
      for (const table of testTables) {
        try {
          await supabase.rpc('exec', { sql: `DROP TABLE IF EXISTS ${table}` });
        } catch (err) {
          // Ignore cleanup errors
        }
      }
    }
    
    console.log('✅ Database setup completed successfully!');
    console.log('\n🎉 Your WordPress to Supabase migration app is ready to use!');
    console.log('\nNext steps:');
    console.log('1. Run: npm run dev');
    console.log('2. Open: http://localhost:3000');
    console.log('3. Test the WordPress connection');
    console.log('4. Start your migration!');
    
    return true;
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.log('\n📋 Manual Setup Required:');
    console.log('Please run the SQL script manually in your Supabase dashboard:');
    console.log('File: scripts/setup-supabase.sql');
    return false;
  }
}

// Check if we can connect to Supabase
async function checkConnection() {
  console.log('🔗 Checking Supabase connection...');
  
  try {
    const { data, error } = await supabase.from('_supabase_migrations').select('*').limit(1);
    if (error && !error.message.includes('does not exist')) {
      throw error;
    }
    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error.message);
    console.log('\n🔧 Please check your Supabase configuration:');
    console.log('- Verify NEXT_PUBLIC_SUPABASE_URL is correct');
    console.log('- Verify SUPABASE_SERVICE_ROLE_KEY is correct');
    console.log('- Ensure your Supabase project is active');
    return false;
  }
}

// Main execution
async function main() {
  console.log('🏗️  WordPress to Supabase Migration - Database Setup\n');
  
  const connectionOk = await checkConnection();
  if (!connectionOk) {
    process.exit(1);
  }
  
  const setupOk = await setupDatabase();
  if (!setupOk) {
    process.exit(1);
  }
  
  console.log('\n🎯 Setup complete! Happy migrating! 🚀');
}

main().catch(console.error);
