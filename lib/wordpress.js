import WPAPI from 'wpapi';

// Create WordPress API client
export function createWPClient(siteUrl, username, password) {
  const endpoint = siteUrl.endsWith('/') ? `${siteUrl}wp-json` : `${siteUrl}/wp-json`;
  
  return new WPAPI({
    endpoint,
    username,
    password
  });
}

// Test WordPress connection
export async function testWordPressConnection(siteUrl, username, password) {
  try {
    const wp = createWPClient(siteUrl, username, password);
    
    // Try to fetch a single post to test connection
    const posts = await wp.posts().perPage(1);
    
    return {
      success: true,
      message: 'Connection successful',
      data: {
        postsFound: posts.length > 0,
        siteUrl: siteUrl
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Connection failed',
      error: error
    };
  }
}

// Discover all WordPress content
export async function discoverWordPressContent(siteUrl, username, password) {
  try {
    const wp = createWPClient(siteUrl, username, password);
    
    // Get content counts
    const [posts, pages, media, categories, tags] = await Promise.all([
      wp.posts().perPage(1).then(data => ({ count: data._paging?.total || 0, sample: data[0] })),
      wp.pages().perPage(1).then(data => ({ count: data._paging?.total || 0, sample: data[0] })),
      wp.media().perPage(1).then(data => ({ count: data._paging?.total || 0, sample: data[0] })),
      wp.categories().perPage(1).then(data => ({ count: data._paging?.total || 0, sample: data[0] })),
      wp.tags().perPage(1).then(data => ({ count: data._paging?.total || 0, sample: data[0] }))
    ]);

    const totalItems = posts.count + pages.count + media.count;

    return {
      success: true,
      content: {
        posts: posts.count,
        pages: pages.count,
        media: media.count,
        categories: categories.count,
        tags: tags.count,
        total: totalItems
      },
      samples: {
        post: posts.sample,
        page: pages.sample,
        media: media.sample
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'Failed to discover content',
      error: error
    };
  }
}

// Fetch all posts with pagination
export async function fetchAllPosts(wp, onProgress = null) {
  const allPosts = [];
  let page = 1;
  const perPage = 50; // Reasonable batch size
  
  try {
    while (true) {
      const posts = await wp.posts()
        .perPage(perPage)
        .page(page)
        .embed(); // Include embedded data like featured media
      
      if (posts.length === 0) break;
      
      allPosts.push(...posts);
      
      if (onProgress) {
        onProgress({
          type: 'posts',
          current: allPosts.length,
          batch: posts.length,
          page
        });
      }
      
      page++;
      
      // Prevent infinite loops
      if (page > 1000) {
        console.warn('Reached maximum page limit for posts');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching posts:', error);
    throw error;
  }
  
  return allPosts;
}

// Fetch all pages with pagination
export async function fetchAllPages(wp, onProgress = null) {
  const allPages = [];
  let page = 1;
  const perPage = 50;
  
  try {
    while (true) {
      const pages = await wp.pages()
        .perPage(perPage)
        .page(page)
        .embed();
      
      if (pages.length === 0) break;
      
      allPages.push(...pages);
      
      if (onProgress) {
        onProgress({
          type: 'pages',
          current: allPages.length,
          batch: pages.length,
          page
        });
      }
      
      page++;
      
      if (page > 1000) {
        console.warn('Reached maximum page limit for pages');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching pages:', error);
    throw error;
  }
  
  return allPages;
}

// Fetch all media with pagination
export async function fetchAllMedia(wp, onProgress = null) {
  const allMedia = [];
  let page = 1;
  const perPage = 50;
  
  try {
    while (true) {
      const media = await wp.media()
        .perPage(perPage)
        .page(page);
      
      if (media.length === 0) break;
      
      allMedia.push(...media);
      
      if (onProgress) {
        onProgress({
          type: 'media',
          current: allMedia.length,
          batch: media.length,
          page
        });
      }
      
      page++;
      
      if (page > 1000) {
        console.warn('Reached maximum page limit for media');
        break;
      }
    }
  } catch (error) {
    console.error('Error fetching media:', error);
    throw error;
  }
  
  return allMedia;
}

// Download media file from WordPress
export async function downloadMediaFile(mediaUrl) {
  try {
    const response = await fetch(mediaUrl);
    if (!response.ok) {
      throw new Error(`Failed to download media: ${response.statusText}`);
    }
    
    const buffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    
    return {
      buffer: Buffer.from(buffer),
      contentType,
      size: buffer.byteLength
    };
  } catch (error) {
    console.error('Error downloading media file:', error);
    throw error;
  }
}

// Extract categories and tags from post
export function extractPostTaxonomies(post) {
  const categories = [];
  const tags = [];
  
  // Extract from embedded data if available
  if (post._embedded) {
    if (post._embedded['wp:term']) {
      post._embedded['wp:term'].forEach(termGroup => {
        termGroup.forEach(term => {
          if (term.taxonomy === 'category') {
            categories.push(term.name);
          } else if (term.taxonomy === 'post_tag') {
            tags.push(term.name);
          }
        });
      });
    }
  }
  
  return { categories, tags };
}

// Clean and prepare content for migration
export function prepareContentForMigration(content, mediaMapping = {}) {
  if (!content) return '';
  
  let cleanContent = content;
  
  // Replace WordPress media URLs with Supabase URLs
  Object.entries(mediaMapping).forEach(([wpUrl, supabaseUrl]) => {
    const regex = new RegExp(wpUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    cleanContent = cleanContent.replace(regex, supabaseUrl);
  });
  
  // Clean up WordPress-specific shortcodes that won't work in static sites
  cleanContent = cleanContent.replace(/\[gallery[^\]]*\]/g, '<!-- Gallery removed during migration -->');
  cleanContent = cleanContent.replace(/\[caption[^\]]*\](.*?)\[\/caption\]/g, '$1');
  
  return cleanContent;
}

// Get WordPress site info
export async function getWordPressSiteInfo(wp) {
  try {
    // This endpoint might not be available on all WordPress sites
    const siteInfo = await wp.root();
    return {
      name: siteInfo.name || 'Unknown',
      description: siteInfo.description || '',
      url: siteInfo.url || '',
      timezone: siteInfo.timezone_string || 'UTC'
    };
  } catch (error) {
    console.warn('Could not fetch site info:', error.message);
    return {
      name: 'WordPress Site',
      description: '',
      url: '',
      timezone: 'UTC'
    };
  }
}
