import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Client for server-side operations with service role
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Client for client-side operations
export const supabase = createClient(
  supabaseUrl, 
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

// Create migration tables with unique suffix
export async function createMigrationTables(suffix) {
  const tables = [
    {
      name: `wp_posts_${suffix}`,
      sql: `
        CREATE TABLE IF NOT EXISTS wp_posts_${suffix} (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          content TEXT,
          excerpt TEXT,
          slug TEXT,
          status TEXT,
          type TEXT,
          author_id INTEGER,
          featured_media_id INTEGER,
          categories TEXT[],
          tags TEXT[],
          created_at TIMESTAMP WITH TIME ZONE,
          modified_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: `wp_pages_${suffix}`,
      sql: `
        CREATE TABLE IF NOT EXISTS wp_pages_${suffix} (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          content TEXT,
          slug TEXT,
          status TEXT,
          parent_id INTEGER,
          template TEXT,
          featured_media_id INTEGER,
          created_at TIMESTAMP WITH TIME ZONE,
          modified_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: `wp_media_${suffix}`,
      sql: `
        CREATE TABLE IF NOT EXISTS wp_media_${suffix} (
          id SERIAL PRIMARY KEY,
          wp_id INTEGER NOT NULL,
          title TEXT,
          filename TEXT,
          original_url TEXT,
          supabase_url TEXT,
          mime_type TEXT,
          file_size INTEGER,
          width INTEGER,
          height INTEGER,
          alt_text TEXT,
          caption TEXT,
          created_at TIMESTAMP WITH TIME ZONE,
          migrated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: `migration_jobs`,
      sql: `
        CREATE TABLE IF NOT EXISTS migration_jobs (
          id SERIAL PRIMARY KEY,
          domain TEXT NOT NULL,
          suffix TEXT NOT NULL,
          status TEXT DEFAULT 'pending',
          progress JSONB DEFAULT '{}',
          total_items INTEGER DEFAULT 0,
          completed_items INTEGER DEFAULT 0,
          error_log TEXT[],
          started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE,
          last_checkpoint JSONB DEFAULT '{}'
        );
      `
    }
  ];

  const results = [];
  for (const table of tables) {
    try {
      const { data, error } = await supabaseAdmin.rpc('exec_sql', {
        sql: table.sql
      });
      
      if (error) {
        // Fallback: try direct table creation
        console.log(`Creating table ${table.name} directly...`);
        // Note: This would need to be handled via Supabase dashboard or SQL editor
        // as direct DDL execution might not be available via RPC
      }
      
      results.push({ table: table.name, success: !error, error });
    } catch (err) {
      results.push({ table: table.name, success: false, error: err.message });
    }
  }

  return results;
}

// Enable RLS and create policies
export async function setupRLSPolicies(suffix) {
  const tables = [`wp_posts_${suffix}`, `wp_pages_${suffix}`, `wp_media_${suffix}`];
  
  for (const table of tables) {
    // Enable RLS
    await supabaseAdmin.rpc('enable_rls', { table_name: table });

    // Create read-only policy for authenticated users
    await supabaseAdmin.rpc('create_policy', {
      table_name: table,
      name: `${table}_read_policy`,
      definition: `
        CREATE POLICY "${table}_read_policy"
        ON "${table}"
        FOR SELECT
        TO authenticated
        USING (true)
      `
    });

    // Create insert/update policy for service role only
    await supabaseAdmin.rpc('create_policy', {
      table_name: table,
      name: `${table}_write_policy`,
      definition: `
        CREATE POLICY "${table}_write_policy"
        ON "${table}"
        FOR ALL
        TO service_role
        USING (true)
        WITH CHECK (true)
      `
    });
  }
}

// Create storage bucket for media files
export async function createMediaBucket(suffix) {
  const bucketName = `wp-media-${suffix}`;
  
  const { data, error } = await supabaseAdmin.storage.createBucket(bucketName, {
    public: true,
    allowedMimeTypes: ['image/*', 'video/*', 'audio/*', 'application/pdf'],
    fileSizeLimit: 50 * 1024 * 1024 // 50MB
  });

  if (error && error.message !== 'Bucket already exists') {
    throw error;
  }

  return { bucketName, data, error };
}

// Upload media file to Supabase storage
export async function uploadMediaFile(bucketName, fileName, fileBuffer, mimeType) {
  const { data, error } = await supabaseAdmin.storage
    .from(bucketName)
    .upload(fileName, fileBuffer, {
      contentType: mimeType,
      upsert: true
    });

  if (error) throw error;

  // Get public URL
  const { data: urlData } = supabaseAdmin.storage
    .from(bucketName)
    .getPublicUrl(fileName);

  return {
    path: data.path,
    publicUrl: urlData.publicUrl
  };
}

// Log migration progress
export async function logMigrationProgress(jobId, progress) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .update({
      progress,
      completed_items: progress.completed || 0,
      last_checkpoint: progress.checkpoint || {},
      status: progress.status || 'running'
    })
    .eq('id', jobId);

  if (error) throw error;
  return data;
}

// Get migration job status
export async function getMigrationJob(jobId) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (error) throw error;
  return data;
}

// Create new migration job
export async function createMigrationJob(domain, suffix, totalItems = 0) {
  const { data, error } = await supabaseAdmin
    .from('migration_jobs')
    .insert({
      domain,
      suffix,
      total_items: totalItems,
      status: 'pending'
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

// Get migration summary
export async function getMigrationSummary(suffix) {
  const tables = [`wp_posts_${suffix}`, `wp_pages_${suffix}`, `wp_media_${suffix}`];
  const summary = {};

  for (const table of tables) {
    try {
      const { count, error } = await supabaseAdmin
        .from(table)
        .select('*', { count: 'exact', head: true });

      if (!error) {
        summary[table] = count;
      }
    } catch (err) {
      summary[table] = 0;
    }
  }

  // Get bucket info
  try {
    const { data: buckets } = await supabaseAdmin.storage.listBuckets();
    const mediaBucket = buckets?.find(b => b.name.includes(suffix));
    if (mediaBucket) {
      const { data: files } = await supabaseAdmin.storage
        .from(mediaBucket.name)
        .list();
      summary.media_files = files?.length || 0;
    }
  } catch (err) {
    summary.media_files = 0;
  }

  return summary;
}
