# Deployment Guide

This guide covers deploying your WordPress to Supabase migration tool to various platforms.

## 🚀 Vercel Deployment (Recommended)

Vercel is the easiest way to deploy this Next.js application.

### Prerequisites

- GitHub account
- Vercel account (free tier available)
- Completed Supabase setup

### Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "WordPress to Supabase migration tool"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select the repository

3. **Configure Environment Variables**
   In Vercel dashboard, add these environment variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   SUPABASE_ACCESS_TOKEN=your-access-token
   ```

4. **Deploy**
   - Click "Deploy"
   - Wait for build to complete
   - Your app will be available at `https://your-app.vercel.app`

### Custom Domain (Optional)

1. In Vercel dashboard, go to your project
2. Click "Settings" → "Domains"
3. Add your custom domain
4. Follow DNS configuration instructions

## 🐳 Docker Deployment

Deploy using Docker for more control over the environment.

### Dockerfile

Create a `Dockerfile` in the root directory:

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'
services:
  wp-migration:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_ACCESS_TOKEN=${SUPABASE_ACCESS_TOKEN}
    env_file:
      - .env.local
```

### Deploy Commands

```bash
# Build and run
docker-compose up --build

# Run in background
docker-compose up -d

# Stop
docker-compose down
```

## ☁️ AWS Deployment

Deploy to AWS using various services.

### AWS Amplify

1. **Connect Repository**
   - Go to AWS Amplify Console
   - Click "New App" → "Host web app"
   - Connect your GitHub repository

2. **Configure Build Settings**
   ```yaml
   version: 1
   frontend:
     phases:
       preBuild:
         commands:
           - npm ci
       build:
         commands:
           - npm run build
     artifacts:
       baseDirectory: .next
       files:
         - '**/*'
     cache:
       paths:
         - node_modules/**/*
   ```

3. **Environment Variables**
   Add the same environment variables as Vercel

### AWS EC2

1. **Launch EC2 Instance**
   - Choose Ubuntu 20.04 LTS
   - t2.micro for testing (free tier)
   - Configure security groups (HTTP/HTTPS)

2. **Install Dependencies**
   ```bash
   sudo apt update
   sudo apt install nodejs npm nginx
   sudo npm install -g pm2
   ```

3. **Deploy Application**
   ```bash
   git clone your-repo
   cd your-app
   npm install
   npm run build
   pm2 start npm --name "wp-migration" -- start
   pm2 startup
   pm2 save
   ```

4. **Configure Nginx**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🌐 Netlify Deployment

Deploy to Netlify for static site hosting.

### Steps

1. **Connect Repository**
   - Go to [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your repository

2. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `.next`

3. **Environment Variables**
   Add the same environment variables

### Netlify Functions

For API routes, you may need to configure Netlify Functions or use a different deployment method since this is a full-stack Next.js app.

## 🔧 Environment Configuration

### Production Environment Variables

```env
# Supabase (Required)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ACCESS_TOKEN=your-access-token

# Optional: Analytics
NEXT_PUBLIC_GA_ID=your-google-analytics-id

# Optional: Error Tracking
SENTRY_DSN=your-sentry-dsn

# Optional: Default WordPress Site
DEFAULT_WP_SITE=https://demo-site.com
DEFAULT_WP_USERNAME=demo-user
DEFAULT_WP_PASSWORD=demo-app-password
```

### Security Considerations

1. **Never commit sensitive keys to Git**
2. **Use environment variables for all secrets**
3. **Rotate keys regularly**
4. **Use HTTPS in production**
5. **Configure CORS properly in Supabase**

## 📊 Monitoring & Analytics

### Error Tracking

Add Sentry for error tracking:

```bash
npm install @sentry/nextjs
```

Configure in `next.config.js`:

```javascript
const { withSentryConfig } = require('@sentry/nextjs');

module.exports = withSentryConfig({
  // Your existing config
}, {
  silent: true,
  org: "your-org",
  project: "your-project",
});
```

### Analytics

Add Google Analytics:

```bash
npm install @next/third-parties
```

### Performance Monitoring

- Use Vercel Analytics (if deployed on Vercel)
- Configure Supabase monitoring
- Set up uptime monitoring (UptimeRobot, Pingdom)

## 🔄 CI/CD Pipeline

### GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🚨 Troubleshooting

### Common Issues

**Build Failures**
- Check Node.js version (18+ required)
- Verify all dependencies are installed
- Check for TypeScript errors

**Environment Variables**
- Ensure all required variables are set
- Check variable names (case-sensitive)
- Verify Supabase keys are correct

**API Routes Not Working**
- Ensure deployment platform supports API routes
- Check serverless function limits
- Verify CORS configuration

**Database Connection Issues**
- Check Supabase service role permissions
- Verify RLS policies are set up correctly
- Test database connection manually

### Getting Help

1. Check deployment platform documentation
2. Review application logs
3. Test locally first
4. Check Supabase dashboard for errors
5. Create GitHub issue with detailed information

## 📈 Scaling Considerations

### Performance Optimization

- Enable Next.js Image Optimization
- Use CDN for static assets
- Implement caching strategies
- Optimize database queries

### High Traffic

- Use Supabase Pro plan for higher limits
- Implement rate limiting
- Consider database connection pooling
- Monitor resource usage

### Multiple Environments

Set up staging and production environments:
- `staging.your-app.com`
- `your-app.com`

Use different Supabase projects for each environment.

Happy deploying! 🚀
